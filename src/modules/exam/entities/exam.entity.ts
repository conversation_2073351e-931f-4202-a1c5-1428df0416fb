import { Enti<PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn } from 'typeorm';
import BaseEntity from 'src/core/entities/base-entity';
import { User } from 'src/modules/user/entities/user.entity';
import { Worksheet } from 'src/modules/worksheet/entities/worksheet.entity';

export enum ExamStatus {
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Entity('exams')
export class Exam extends BaseEntity {
  @ManyToOne(() => User, { eager: false, nullable: false })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  userId: string;

  @ManyToOne(() => Worksheet, { eager: false, nullable: false })
  @JoinColumn({ name: 'worksheetId' })
  worksheet: Worksheet;

  @Column()
  worksheetId: string;

  @Column({ length: 255 })
  title: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'jsonb', nullable: true })
  selectedOptions?: any;

  @Column({
    type: 'enum',
    enum: ExamStatus,
    default: ExamStatus.IN_PROGRESS,
  })
  status: ExamStatus;

  @Column({ type: 'jsonb', nullable: true })
  questions?: any;

  @Column({ default: 1 })
  maxAttempts: number;
}
