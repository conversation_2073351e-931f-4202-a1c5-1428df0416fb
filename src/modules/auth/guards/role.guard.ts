import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { EUserRole } from 'src/modules/user/dto/create-user.dto';

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  // Check if user has required roles
  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get<EUserRole[]>(
      'roles',
      context.getHandler(),
    );

    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return false;
    }

    // Admin has access to all resources
    if (user.role === EUserRole.ADMIN) {
      return true;
    }

    // Check if user's role is in the required roles
    return requiredRoles.includes(user.role);
  }
}
