import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EUserRole } from 'src/modules/user/dto/create-user.dto';

export class SignUpDto {
  @ApiProperty({
    description: 'User role',
    enum: EUserRole,
    default: EUserRole.TEACHER,
  })
  @IsOptional()
  @IsEnum(EUserRole)
  role?: EUserRole;

  @ApiPropertyOptional({
    description: 'School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  schoolId?: string;
  @ApiProperty({
    description: 'User full name',
    example: '<PERSON>',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password (minimum 6 characters)',
    example: 'password123',
    minLength: 6,
  })
  @IsNotEmpty()
  @MinLength(6)
  password: string;
}
