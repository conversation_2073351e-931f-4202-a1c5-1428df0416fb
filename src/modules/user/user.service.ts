import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { FilterUserDto } from './dto/filter-user.dto';
import { User } from './entities/user.entity';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }

  async findAll(filterUserDto?: FilterUserDto): Promise<User[]> {
    const { schoolId, role } = filterUserDto || {};

    // Build where conditions based on provided filters
    const whereConditions: any = {};

    if (schoolId) {
      whereConditions.schoolId = schoolId;
    }

    if (role) {
      whereConditions.role = role;
    }

    // If no filters provided, return all users (maintain backward compatibility)
    if (Object.keys(whereConditions).length === 0) {
      return this.userRepository.find();
    }

    // Apply filters using TypeORM's find with where conditions
    return this.userRepository.find({ where: whereConditions });
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email } }); // Ensure this query works
  }

  async findById(id: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { id } });
  }

  async findOne(id: string): Promise<User | null> {
    return this.findById(id);
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findById(id);

    if (!user) {
      throw new Error('User not found');
    }

    // If password is provided, it will be hashed in the BeforeUpdate hook
    if (updateUserDto.password) {
      const bcrypt = await import('bcrypt');
      updateUserDto.password = await bcrypt.hash(updateUserDto.password, 10);
    }

    // Update user properties
    Object.assign(user, updateUserDto);

    return this.userRepository.save(user);
  }
}
