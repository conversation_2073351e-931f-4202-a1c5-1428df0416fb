import { <PERSON>, Post, Get, Body, Param, Req } from '@nestjs/common';
import { ExamService } from './exam.service';
import { CreateExamDto } from './dto/create-exam.dto';
import { SubmitExamDto } from './dto/submit-exam.dto';
import { ActiveUser } from '../auth/decorators/active-user.decorator';

@Controller('exams')
export class ExamController {
  constructor(private readonly examService: ExamService) {}

  @Post()
  async createExam(@ActiveUser() user, @Body() dto: CreateExamDto) {
    const userId = user.sub;
    return this.examService.createExam(userId, dto);
  }

  @Get(':id')
  async getExam(@ActiveUser() user, @Param('id') id: string) {
    const userId = user.sub;
    const role = user.role;
    return this.examService.getExamById(userId, id, role);
  }

  @Post(':id/submit')
  async submitExam(
    @ActiveUser() user,
    @Param('id') id: string,
    @Body() dto: SubmitExamDto,
  ) {
    const userId = user.sub;
    return this.examService.submitExam(userId, id, dto);
  }

  @Get('by-worksheet/:worksheetId')
  async getExamsByWorksheet(@Param('worksheetId') worksheetId: string) {
    return this.examService.getExamsByWorksheetId(worksheetId);
  }
}
