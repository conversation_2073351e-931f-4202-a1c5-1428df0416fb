import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { School } from './entities/school.entity';
import { CreateSchoolDto } from './dto/create-school.dto';
import { UpdateSchoolDto } from './dto/update-school.dto';
import { BrandService } from '../brand/brand.service';
import { UserService } from '../user/user.service';
import { EUserRole } from '../user/dto/create-user.dto';

@Injectable()
export class SchoolService {
  constructor(
    @InjectRepository(School)
    private schoolRepository: Repository<School>,
    private brandService: BrandService,
    private userService: UserService,
  ) {}

  async create(createSchoolDto: CreateSchoolDto): Promise<School> {
    const { adminId, brandId, ...schoolData } = createSchoolDto;

    // Validate admin user if provided
    if (adminId) {
      const admin = await this.userService.findOne(adminId);
      if (!admin){
        throw new NotFoundException(`Admin user with ID ${adminId} not found`);
      }

      if (admin.role !== EUserRole.SCHOOL_MANAGER) {
        throw new BadRequestException('Admin user must have SCHOOL_MANAGER role');
      }
    }

    // Validate brand if provided
    if (brandId) {
      await this.brandService.findOne(brandId);
    }

    const school = this.schoolRepository.create({
      ...schoolData,
      adminId,
      brandId,
    });

    return this.schoolRepository.save(school);
  }

  async findAll(): Promise<School[]> {
    return this.schoolRepository.find({
      relations: ['admin', 'brand'],
    });
  }

  async findOne(id: string): Promise<School> {
    const school = await this.schoolRepository.findOne({
      where: { id },
      relations: ['admin', 'brand'],
    });
    
    if (!school) {
      throw new NotFoundException(`School with ID ${id} not found`);
    }
    
    return school;
  }

  async update(id: string, updateSchoolDto: UpdateSchoolDto): Promise<School> {
    const { adminId, brandId, ...schoolData } = updateSchoolDto;

    // Validate admin user if provided
    if (adminId) {
      const admin = await this.userService.findOne(adminId);
      if (!admin){
        throw new NotFoundException(`Admin user with ID ${adminId} not found`);
      }
      if (admin.role !== EUserRole.SCHOOL_MANAGER) {
        throw new BadRequestException('Admin user must have SCHOOL_MANAGER role');
      }
    }

    // Validate brand if provided
    if (brandId) {
      await this.brandService.findOne(brandId);
    }

    const school = await this.findOne(id);
    this.schoolRepository.merge(school, {
      ...schoolData,
      adminId,
      brandId,
    });
    
    return this.schoolRepository.save(school);
  }

  async remove(id: string): Promise<void> {
    const school = await this.findOne(id);
    await this.schoolRepository.remove(school);
  }
}